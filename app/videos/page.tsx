"use client"

import { useState, useEffect } from "react"
import { ViewToggle } from "@/components/ui/view-toggle"
import { VideosGridView } from "@/components/videos/videos-grid-view"
import { VideosListView } from "@/components/videos/videos-list-view"
import { AddVideoModal } from "@/components/videos/add-video-modal"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Plus } from "lucide-react"

interface Video {
  id: number
  title: string
  minister: string
  duration: string
  publishedDate: string
  transcriptionStatus: string
  transcriptionProgress?: number
  subjects: string[]
  thumbnail: string
  views: number
  description: string
  youtubeId?: string
}

export default function VideosPage() {
  const [view, setView] = useState<"grid" | "list">("grid")
  const [searchTerm, setSearchTerm] = useState("")
  const [showAddModal, setShowAddModal] = useState(false)
  const [sortBy, setSortBy] = useState("publishedDate")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [videos, setVideos] = useState<Video[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchVideos = async () => {
      setLoading(true)
      try {
        const response = await fetch('/api/videos')
        if (response.ok) {
          const fetchedVideos = await response.json()
          setVideos(fetchedVideos)
        } else {
          console.error("Failed to fetch videos")
          setVideos([])
        }
      } catch (error) {
        console.error("Error fetching videos:", error)
        setVideos([])
      } finally {
        setLoading(false)
      }
    }

    fetchVideos()
  }, [])

  const filteredVideos = videos
    .filter(
      (video) =>
        video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        video.minister.toLowerCase().includes(searchTerm.toLowerCase()) ||
        video.subjects.some((subject) => subject.toLowerCase().includes(searchTerm.toLowerCase())),
    )
    .sort((a, b) => {
      const aValue = a[sortBy as keyof typeof a]
      const bValue = b[sortBy as keyof typeof b]
      const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      return sortOrder === "asc" ? comparison : -comparison
    })

  return (
    <div className="min-h-screen bg-gray-50">
      <main className="p-6">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Video Library</h1>
              <p className="text-gray-600">Manage sermons, teachings, and spiritual content</p>
            </div>
            <div className="flex items-center space-x-2">
              <ViewToggle view={view} onViewChange={setView} />
              <Button onClick={() => setShowAddModal(true)} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Add Video
              </Button>
            </div>
          </div>

          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search videos, ministers, or subjects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : view === "grid" ? (
          <VideosGridView videos={filteredVideos} />
        ) : (
          <VideosListView
            videos={filteredVideos}
            sortBy={sortBy}
            setSortBy={setSortBy}
            sortOrder={sortOrder}
            setSortOrder={setSortOrder}
          />
        )}
      </main>

      <AddVideoModal open={showAddModal} onOpenChange={setShowAddModal} />
    </div>
  )
}
