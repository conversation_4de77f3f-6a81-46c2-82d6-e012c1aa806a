import { type NextRequest, NextResponse } from "next/server"
import { neon } from "@neondatabase/serverless"
import { isDatabaseConfigured } from "@/lib/database"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const minister = searchParams.get("minister") || "all"
    const sortBy = searchParams.get("sortBy") || "published_date"
    const sortOrder = searchParams.get("sortOrder") || "desc"

    if (!isDatabaseConfigured()) {
      return NextResponse.json([], { status: 500 })
    }

    const sql = neon(process.env.DATABASE_URL!)

    // Get videos from database
    const videos = await sql`
      SELECT
        v.id,
        v.title,
        v.minister,
        v.duration,
        v.published_date,
        v.youtube_id,
        v.subjects,
        li.description,
        li.cover_image_url
      FROM videos v
      LEFT JOIN library_items li ON v.transcript_id = li.id
      ORDER BY v.published_date DESC
    `

    // Transform the data to match the expected format
    let transformedVideos = videos.map((video: any) => ({
      id: video.id,
      title: video.title || "Untitled Video",
      minister: video.minister || "Unknown Minister",
      duration: video.duration || "00:00",
      publishedDate: video.published_date ? new Date(video.published_date).toISOString().split('T')[0] : "",
      transcriptionStatus: "completed", // Assume completed if linked to transcript
      subjects: Array.isArray(video.subjects) ? video.subjects : [],
      thumbnail: video.cover_image_url || "/placeholder.svg?height=120&width=200",
      views: Math.floor(Math.random() * 2000) + 100, // Random views for demo
      description: video.description || "No description available",
      youtubeId: video.youtube_id || ""
    }))

    // Apply search filter
    if (search) {
      transformedVideos = transformedVideos.filter(
        (video) =>
          video.title.toLowerCase().includes(search.toLowerCase()) ||
          video.minister.toLowerCase().includes(search.toLowerCase()) ||
          video.description.toLowerCase().includes(search.toLowerCase()) ||
          video.subjects.some((subject: string) => subject.toLowerCase().includes(search.toLowerCase()))
      )
    }

    // Apply minister filter
    if (minister !== "all") {
      transformedVideos = transformedVideos.filter((video) => video.minister === minister)
    }

    return NextResponse.json(transformedVideos)
  } catch (error) {
    console.error("Error fetching videos:", error)
    return NextResponse.json([], { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, minister, youtubeUrl, description, subjects } = body

    // For now, just return success since we don't have a videos table
    const mockVideo = {
      id: Date.now(),
      title,
      minister,
      youtube_url: youtubeUrl,
      description,
      subjects: subjects.split(",").map((s: string) => s.trim()),
      duration: "00:00",
      created_at: new Date().toISOString(),
    }

    return NextResponse.json({
      success: true,
      video: mockVideo,
    })
  } catch (error) {
    console.error("Error creating video:", error)
    return NextResponse.json({ success: false, error: "Failed to create video" }, { status: 500 })
  }
}
