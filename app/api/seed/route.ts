import { NextResponse } from "next/server"
import { query, isDatabaseConfigured } from "@/lib/db"
import fs from "fs"
import path from "path"

export async function POST() {
  try {
    // Check if database is configured
    if (!isDatabaseConfigured()) {
      return NextResponse.json(
        {
          success: false,
          message: "Database not configured. Please check your DATABASE_URL environment variable.",
        },
        { status: 500 },
      )
    }

    // Read and execute schema
    const schemaPath = path.join(process.cwd(), "scripts", "unified-schema.sql")
    const seedPath = path.join(process.cwd(), "scripts", "unified-seed-data.sql")

    if (!fs.existsSync(schemaPath)) {
      return NextResponse.json(
        {
          success: false,
          message: "Schema file not found at scripts/unified-schema.sql",
        },
        { status: 404 },
      )
    }

    if (!fs.existsSync(seedPath)) {
      return NextResponse.json(
        {
          success: false,
          message: "Seed data file not found at scripts/unified-seed-data.sql",
        },
        { status: 404 },
      )
    }

    const schemaSQL = fs.readFileSync(schemaPath, "utf8")
    const seedSQL = fs.readFileSync(seedPath, "utf8")

    // Execute schema creation
    console.log("Creating database schema...")
    const schemaStatements = schemaSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    for (const statement of schemaStatements) {
      if (statement.trim()) {
        await query(statement)
      }
    }

    // Execute seed data
    console.log("Inserting seed data...")
    const seedStatements = seedSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    for (const statement of seedStatements) {
      if (statement.trim()) {
        await query(statement)
      }
    }

    // Verify the seeding worked
    const libraryCount = await query("SELECT COUNT(*) as count FROM library_items")
    const chaptersCount = await query("SELECT COUNT(*) as count FROM chapters")
    const usersCount = await query("SELECT COUNT(*) as count FROM users")
    const subjectsCount = await query("SELECT COUNT(*) as count FROM subjects")

    return NextResponse.json({
      success: true,
      message: "Database seeded successfully",
      counts: {
        libraryItems: libraryCount.rows[0]?.count || 0,
        chapters: chaptersCount.rows[0]?.count || 0,
        users: usersCount.rows[0]?.count || 0,
        subjects: subjectsCount.rows[0]?.count || 0,
      },
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Seeding error:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Failed to seed database",
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}

export async function GET() {
  try {
    if (!isDatabaseConfigured()) {
      return NextResponse.json(
        {
          success: false,
          message: "Database not configured",
        },
        { status: 500 },
      )
    }

    // Check current database state
    const libraryCount = await query("SELECT COUNT(*) as count FROM library_items")
    const chaptersCount = await query("SELECT COUNT(*) as count FROM chapters")
    const usersCount = await query("SELECT COUNT(*) as count FROM users")
    const subjectsCount = await query("SELECT COUNT(*) as count FROM subjects")

    return NextResponse.json({
      success: true,
      message: "Database status retrieved",
      counts: {
        libraryItems: libraryCount.rows[0]?.count || 0,
        chapters: chaptersCount.rows[0]?.count || 0,
        users: usersCount.rows[0]?.count || 0,
        subjects: subjectsCount.rows[0]?.count || 0,
      },
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Database status error:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Failed to get database status",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    )
  }
}
