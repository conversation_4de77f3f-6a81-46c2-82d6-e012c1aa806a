import { NextResponse } from "next/server"
import { query } from "@/lib/db"

export async function POST() {
  try {
    console.log("Starting quick seed...")

    // Insert a simple book first
    const bookResult = await query(`
      INSERT INTO library_items (title, author, type, status, category, description, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
      RETURNING id
    `, [
      'Foundations of Faith',
      '<PERSON>', 
      'Book',
      'Published',
      'Theology',
      'A comprehensive guide to understanding the fundamental principles of Christian faith.'
    ])

    console.log("Book inserted:", bookResult)

    // Insert a chapter for this book
    if (bookResult.rows && bookResult.rows.length > 0) {
      const bookId = bookResult.rows[0].id
      
      await query(`
        INSERT INTO chapters (library_item_id, title, content, order_index, word_count, status, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
      `, [
        bookId,
        'The Foundation of Faith',
        'Faith is the cornerstone of our relationship with God. In this chapter, we explore what it means to have faith and how it impacts every aspect of our lives...',
        1,
        2450,
        'Published'
      ])

      console.log("Chapter inserted for book:", bookId)
    }

    // Insert another book
    await query(`
      INSERT INTO library_items (title, author, type, status, category, description, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
    `, [
      'The Power of Prayer',
      'Jane Smith',
      'Book', 
      'Published',
      'Spiritual Life',
      'Exploring the transformative power of prayer in the believer\'s life.'
    ])

    console.log("Second book inserted")

    // Check counts
    const libraryCount = await query("SELECT COUNT(*) as count FROM library_items")
    const chaptersCount = await query("SELECT COUNT(*) as count FROM chapters")

    return NextResponse.json({
      success: true,
      message: "Quick seed completed successfully",
      counts: {
        libraryItems: libraryCount.rows[0]?.count || 0,
        chapters: chaptersCount.rows[0]?.count || 0,
      },
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Quick seed error:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Failed to quick seed database",
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}
