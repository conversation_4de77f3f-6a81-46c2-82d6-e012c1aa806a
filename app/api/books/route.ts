import { type NextRequest, NextResponse } from "next/server"
import { getBooks } from "@/lib/data/books"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search") || ""
    const type = searchParams.get("type") || "all"
    const category = searchParams.get("category") || "all"
    const status = searchParams.get("status") || "all"
    const sortBy = searchParams.get("sortBy") || "title"
    const sortOrder = (searchParams.get("sortOrder") || "asc") as "asc" | "desc"

    const books = await getBooks(search, type, category, status, sortBy, sortOrder)

    return NextResponse.json(books)
  } catch (error) {
    console.error("Error fetching books:", error)
    return NextResponse.json(
      { error: "Failed to fetch books" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, author, type, category, description, tags } = body

    // Import query function for this specific operation
    const { query } = await import("@/lib/db")

    const result = await query(
      `INSERT INTO library_items (title, author, type, category, status, description, tags, created_at, updated_at)
       VALUES ($1, $2, $3, $4, 'Draft', $5, $6, NOW(), NOW())
       RETURNING id, title, author, type, category, status, description, tags, created_at`,
      [title, author, type || 'Book', category, description, tags || []],
    )

    const item = result.rows[0]

    return NextResponse.json({
      success: true,
      book: {
        id: item.id,
        title: item.title,
        author: item.author,
        type: item.type,
        category: item.category,
        status: item.status,
        description: item.description,
        tags: Array.isArray(item.tags) ? item.tags : [],
        createdAt: item.created_at,
      },
    })
  } catch (error) {
    console.error("Error creating book:", error)
    return NextResponse.json({ success: false, error: "Failed to create book" }, { status: 500 })
  }
}
