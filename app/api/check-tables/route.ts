import { NextResponse } from "next/server"
import { neon } from "@neondatabase/serverless"

export async function GET() {
  try {
    if (!process.env.DATABASE_URL) {
      return NextResponse.json({ error: "Database not configured" }, { status: 500 })
    }

    const sql = neon(process.env.DATABASE_URL)
    
    // Check what tables exist
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `

    // Try to check if library_items table exists and has data
    let libraryItemsInfo = null
    try {
      const count = await sql`SELECT COUNT(*) as count FROM library_items`
      const columns = await sql`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'library_items' 
        ORDER BY ordinal_position
      `
      libraryItemsInfo = {
        count: count[0]?.count || 0,
        columns: columns
      }
    } catch (error) {
      libraryItemsInfo = { error: error instanceof Error ? error.message : "Unknown error" }
    }

    return NextResponse.json({
      success: true,
      tables: tables.map(t => t.table_name),
      libraryItemsInfo,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error("Check tables error:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    )
  }
}
